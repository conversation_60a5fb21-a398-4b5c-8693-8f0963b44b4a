/**
 * Hooks barrel export
 * Centralized exports for all custom hooks
 */

// Auth hooks
export { useAuth } from "@/contexts/AuthContext";

// Form hooks (commented out until created)
// export { useFormField } from "@/components/ui/form-hooks";

// Local storage hooks (commented out until created)
// export { useLocalStorage } from "./useLocalStorage";

// API hooks (commented out until created)
// export { useApi } from "./useApi";

// Debounce hooks (commented out until created)
// export { useDebounce } from "./useDebounce";

// Window size hooks (commented out until created)
// export { useWindowSize } from "./useWindowSize";

// Previous value hooks (commented out until created)
// export { usePrevious } from "./usePrevious";

// Toggle hooks (commented out until created)
// export { useToggle } from "./useToggle";
