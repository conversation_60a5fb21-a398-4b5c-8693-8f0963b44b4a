import React, { useState, useEffect, useCallback } from "react";
import {
  TaskTable,
  TaskDetailModal,
  SharedTaskBoard,
  TaskStatsCards,
  CreateTaskWithMilestoneModal,
} from "@/components/tasks";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

import { toast } from "sonner";
import {
  Table as TableIcon,
  Kanban,
  Plus,
  BarChart3,
  Users,
  Filter,
  Target,
  CheckCircle,
  Clock,
  AlertTriangle,
} from "lucide-react";

// Milestone and Task interfaces - compatible with ProjectDetail types
interface Milestone {
  id: string;
  name: string;
  description: string;
  deadline: string;
  status: "Not Started" | "In Progress" | "Completed" | "Overdue";
  progress: number;
  tasks: Task[];
}

// Enhanced Task interface with milestone support - keeping existing status values for compatibility
interface Task {
  id: string;
  title: string;
  description: string;
  status: "Not Started" | "In Progress" | "Complete" | "Overdue";
  dueDate: string;
  priority: "Low" | "Medium" | "High";
  projectTag: string;
  milestoneId?: string; // Link to milestone
  assignedTo: {
    id: string;
    name: string;
    avatar: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Mock milestones data
const mockMilestones: Milestone[] = [
  {
    id: "milestone-1",
    name: "Project Setup & Planning",
    description: "Initial project setup, planning, and team coordination",
    deadline: "2024-02-28T17:00:00Z",
    status: "In Progress",
    progress: 75,
    tasks: [],
  },
  {
    id: "milestone-2",
    name: "Core Development",
    description: "Main development phase including backend and frontend work",
    deadline: "2024-04-30T17:00:00Z",
    status: "Not Started",
    progress: 0,
    tasks: [],
  },
  {
    id: "milestone-3",
    name: "Testing & Deployment",
    description: "Testing, optimization, and deployment preparation",
    deadline: "2024-06-15T17:00:00Z",
    status: "Not Started",
    progress: 0,
    tasks: [],
  },
];

// Enhanced mock data with milestone assignments and updated statuses
const mockTasks: Task[] = [
  {
    id: "1",
    title: "Implement User Authentication",
    description:
      "Set up JWT-based authentication system with login/logout functionality",
    status: "In Progress",
    dueDate: "2024-02-15T17:00:00Z",
    priority: "High",
    projectTag: "Backend API",
    milestoneId: "milestone-2",
    assignedTo: {
      id: "user1",
      name: "Sarah Chen",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-20T14:30:00Z",
  },
  {
    id: "2",
    title: "Design Dashboard UI",
    description: "Create responsive dashboard layout with charts and widgets",
    status: "Complete",
    dueDate: "2024-01-30T17:00:00Z",
    priority: "Medium",
    projectTag: "Frontend",
    milestoneId: "milestone-1",
    assignedTo: {
      id: "user2",
      name: "Michael Rodriguez",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-10T09:00:00Z",
    updatedAt: "2024-01-28T16:45:00Z",
  },
  {
    id: "3",
    title: "Database Migration",
    description: "Migrate legacy database to new PostgreSQL instance",
    status: "Overdue",
    dueDate: "2024-01-20T17:00:00Z",
    priority: "High",
    projectTag: "Database",
    milestoneId: "milestone-1",
    assignedTo: {
      id: "user3",
      name: "Emily Johnson",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-05T08:00:00Z",
    updatedAt: "2024-01-18T11:20:00Z",
  },
  {
    id: "4",
    title: "API Documentation",
    description: "Write comprehensive API documentation using OpenAPI/Swagger",
    status: "Not Started",
    dueDate: "2024-02-20T17:00:00Z",
    priority: "Low",
    projectTag: "Documentation",
    milestoneId: "milestone-2",
    assignedTo: {
      id: "user1",
      name: "Sarah Chen",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-12T14:00:00Z",
    updatedAt: "2024-01-12T14:00:00Z",
  },
  {
    id: "5",
    title: "Performance Optimization",
    description: "Optimize application performance and reduce load times",
    status: "In Progress",
    dueDate: "2024-02-10T17:00:00Z",
    priority: "Medium",
    projectTag: "Performance",
    milestoneId: "milestone-2",
    assignedTo: {
      id: "user2",
      name: "Michael Rodriguez",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-18T11:30:00Z",
    updatedAt: "2024-01-25T09:15:00Z",
  },
  {
    id: "6",
    title: "Mobile App Testing",
    description:
      "Comprehensive testing of mobile application across different devices",
    status: "Not Started",
    dueDate: "2024-02-25T17:00:00Z",
    priority: "Medium",
    projectTag: "Mobile",
    milestoneId: "milestone-3",
    assignedTo: {
      id: "user4",
      name: "David Kim",
      avatar: "",
      email: "<EMAIL>",
    },
    createdAt: "2024-01-20T11:00:00Z",
    updatedAt: "2024-01-20T11:00:00Z",
  },
];

const UserTaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>(mockTasks);
  const [milestones, setMilestones] = useState<Milestone[]>(mockMilestones);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [activeView, setActiveView] = useState<"table" | "kanban">("kanban"); // Default to kanban

  // Role-based permissions (can be made dynamic based on user context)
  const isLeader = true;

  // Calculate milestone progress
  const calculateMilestoneProgress = useCallback(
    (milestoneId: string) => {
      const milestoneTasks = tasks.filter(
        (task) => task.milestoneId === milestoneId
      );
      if (milestoneTasks.length === 0) return 0;

      const completedTasks = milestoneTasks.filter(
        (task) => task.status === "Complete"
      );
      return Math.round((completedTasks.length / milestoneTasks.length) * 100);
    },
    [tasks]
  );

  // Update milestone progress when tasks change
  useEffect(() => {
    setMilestones((prev) =>
      prev.map((milestone) => ({
        ...milestone,
        progress: calculateMilestoneProgress(milestone.id),
        tasks: tasks.filter((task) => task.milestoneId === milestone.id),
      }))
    );
  }, [tasks, calculateMilestoneProgress]);

  // Task event handlers
  const handleTaskEdit = (task: Task) => {
    setSelectedTask(task);
    setIsDetailModalOpen(true);
  };

  const handleTaskView = (task: Task) => {
    setSelectedTask(task);
    setIsDetailModalOpen(true);
  };

  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
    setIsDetailModalOpen(true);
  };

  const handleCreateTaskClick = () => {
    setIsCreateModalOpen(true);
  };

  // Task creation handler
  const handleCreateTaskSubmit = (
    newTask: Omit<Task, "id" | "createdAt" | "updatedAt">
  ) => {
    const task: Task = {
      ...newTask,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setTasks((prev) => [task, ...prev]);
    setIsCreateModalOpen(false);

    // Show success toast
    toast.success("Task created successfully!", {
      description: `"${task.title}" has been added to your task list.`,
    });
  };

  // Task update handler
  const handleUpdateTask = (updatedTask: Task) => {
    const previousTask = tasks.find((task) => task.id === updatedTask.id);
    const statusChanged =
      previousTask && previousTask.status !== updatedTask.status;

    setTasks((prev) =>
      prev.map((task) =>
        task.id === updatedTask.id
          ? { ...updatedTask, updatedAt: new Date().toISOString() }
          : task
      )
    );
    setSelectedTask(updatedTask);

    // Show appropriate toast based on what changed
    if (statusChanged) {
      toast.success("Task status updated!", {
        description: `"${updatedTask.title}" status changed to ${updatedTask.status}.`,
      });
    } else {
      toast.success("Task updated successfully!", {
        description: `"${updatedTask.title}" has been updated.`,
      });
    }
  };

  // Calculate task statistics
  const taskStats = {
    total: tasks.length,
    notStarted: tasks.filter((t) => t.status === "Not Started").length,
    inProgress: tasks.filter((t) => t.status === "In Progress").length,
    completed: tasks.filter((t) => t.status === "Complete").length,
    overdue: tasks.filter((t) => {
      if (t.status === "Complete") return false;
      return new Date() > new Date(t.dueDate);
    }).length,
  };

  // Get unique team members for statistics
  const teamMembers = Array.from(
    new Set(tasks.map((task) => task.assignedTo.id))
  ).length;

  // Get unique project tags
  const projectTags = Array.from(
    new Set(tasks.map((task) => task.projectTag))
  ).length;

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Responsive Header */}
      <div className="sticky top-16 left-0 right-0 z-40 bg-white border-b border-slate-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
          {/* Mobile-first header layout */}
          <div className="space-y-4">
            {/* Title Section */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl font-semibold text-slate-900 tracking-tight truncate">
                  Task Management
                </h1>
                <p className="text-sm text-slate-600 mt-1 hidden sm:block">
                  Organize, track, and manage your team's work with powerful
                  tools
                </p>
              </div>

              {/* Create Task Button - Mobile Priority */}
              {isLeader && (
                <div className="flex-shrink-0">
                  <Button
                    onClick={handleCreateTaskClick}
                    className="bg-emerald-600 hover:bg-emerald -700 cursor-pointer text-white flex items-center space-x-2 w-full sm:w-auto"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Create Task</span>
                  </Button>
                </div>
              )}
            </div>

            {/* Controls Section */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              {/* Quick Stats - Responsive */}
              <div className="flex items-center justify-between sm:justify-start sm:space-x-6 text-sm">
                <div className="flex items-center space-x-1 sm:space-x-2">
                  <BarChart3 className="w-4 h-4 text-slate-400" />
                  <span className="text-slate-600 text-xs sm:text-sm">
                    {taskStats.total} Tasks
                  </span>
                </div>
                <div className="flex items-center space-x-1 sm:space-x-2">
                  <Users className="w-4 h-4 text-slate-400" />
                  <span className="text-slate-600 text-xs sm:text-sm">
                    {teamMembers} Members
                  </span>
                </div>
                <div className="flex items-center space-x-1 sm:space-x-2">
                  <Filter className="w-4 h-4 text-slate-400" />
                  <span className="text-slate-600 text-xs sm:text-sm">
                    {projectTags} Projects
                  </span>
                </div>
              </div>

              {/* View Toggle */}
              <div className="flex items-center space-x-2 bg-slate-100 rounded-lg p-1">
                <Button
                  variant={activeView === "table" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveView("table")}
                  className="flex items-center space-x-2 flex-1 sm:flex-none"
                >
                  <TableIcon className="w-4 h-4" />
                  <span className="text-xs sm:text-sm">Table</span>
                </Button>
                <Button
                  variant={activeView === "kanban" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveView("kanban")}
                  className="flex items-center space-x-2 flex-1 sm:flex-none"
                >
                  <Kanban className="w-4 h-4" />
                  <span className="text-xs sm:text-sm">Kanban</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area - Responsive */}
      <div className="flex-1">
        {activeView === "table" ? (
          <>
            {/* Task Statistics Dashboard - Table View */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4">
              <TaskStatsCards
                stats={taskStats}
                teamMembers={teamMembers}
                projectTags={projectTags}
                showExtendedStats={true}
              />
            </div>

            {/* Table View */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 pb-6">
              <TaskTable
                tasks={tasks}
                onTaskEdit={handleTaskEdit}
                onTaskView={handleTaskView}
                onTaskClick={handleTaskClick}
                onCreateTask={handleCreateTaskClick}
                isLeader={isLeader}
              />
            </div>
          </>
        ) : (
          /* Kanban View - Centered and Responsive */
          <div className="min-h-screen">
            {/* Milestone Overview Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 py-6">
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center space-x-2">
                  <Target className="w-5 h-5 text-blue-600" />
                  <span>Milestones Overview</span>
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {milestones.map((milestone) => (
                    <Card
                      key={milestone.id}
                      className="p-4 border border-slate-200"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          {milestone.status === "Completed" ? (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          ) : milestone.status === "In Progress" ? (
                            <Clock className="w-4 h-4 text-blue-600" />
                          ) : milestone.status === "Overdue" ? (
                            <AlertTriangle className="w-4 h-4 text-red-600" />
                          ) : (
                            <Clock className="w-4 h-4 text-gray-600" />
                          )}
                          <h3 className="font-medium text-slate-900">
                            {milestone.name}
                          </h3>
                        </div>
                        <span className="text-sm font-medium text-blue-600">
                          {milestone.progress}%
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 mb-3">
                        {milestone.description}
                      </p>
                      <div className="space-y-2">
                        <div className="w-full bg-slate-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${milestone.progress}%` }}
                          />
                        </div>
                        <div className="flex justify-between text-xs text-slate-500">
                          <span>{milestone.tasks.length} tasks</span>
                          <span>
                            Due:{" "}
                            {new Date(milestone.deadline).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </div>

            {/* Centered Kanban Board */}
            <div className="flex justify-center">
              <div className="w-full max-w-7xl px-4 sm:px-6">
                <SharedTaskBoard
                  tasks={tasks}
                  onTaskUpdate={handleUpdateTask}
                  onTaskClick={handleTaskClick}
                  isLeader={isLeader}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {/* Task Detail Modal */}
      <TaskDetailModal
        open={isDetailModalOpen}
        onOpenChange={setIsDetailModalOpen}
        task={selectedTask}
        onUpdate={handleUpdateTask}
        isLeader={isLeader}
      />

      {/* Create Task Modal with Milestone Selection */}
      <CreateTaskWithMilestoneModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onCreate={handleCreateTaskSubmit}
        milestones={milestones}
      />
    </div>
  );
};

export default UserTaskManagement;
